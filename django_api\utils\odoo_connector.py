"""
Odoo XML-RPC Connector
Handles communication with Odoo backend via XML-RPC
"""
import xmlrpc.client
import logging
from django.conf import settings
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class OdooConnector:
    """
    Connector class to interface with Odoo via XML-RPC
    """
    
    def __init__(self):
        self.url = getattr(settings, 'ODOO_URL', 'http://localhost:8069')
        self.db = getattr(settings, 'ODOO_DB', 'odoo_erp')
        self.username = getattr(settings, 'ODOO_USERNAME', 'admin')
        self.password = getattr(settings, 'ODOO_PASSWORD', 'admin')
        
        self.common = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/common')
        self.models = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/object')
        self.uid = None
        
        logger.info(f"Initialized Odoo connector for {self.url}")
    
    def authenticate(self) -> bool:
        """
        Authenticate with Odoo
        Returns True if authentication successful, False otherwise
        """
        try:
            self.uid = self.common.authenticate(self.db, self.username, self.password, {})
            if self.uid:
                logger.info(f"Successfully authenticated with Odoo as user ID: {self.uid}")
                return True
            else:
                logger.error("Authentication failed: Invalid credentials")
                return False
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return False
    
    def execute(self, model: str, method: str, *args, **kwargs) -> Any:
        """
        Execute a method on an Odoo model
        
        Args:
            model: Odoo model name (e.g., 'account.move')
            method: Method to execute (e.g., 'search', 'read', 'create')
            *args: Positional arguments for the method
            **kwargs: Keyword arguments for the method
            
        Returns:
            Result of the method execution
        """
        if not self.uid:
            if not self.authenticate():
                raise Exception("Failed to authenticate with Odoo")
        
        try:
            result = self.models.execute_kw(
                self.db, self.uid, self.password,
                model, method, args, kwargs
            )
            logger.debug(f"Executed {model}.{method} successfully")
            return result
        except Exception as e:
            logger.error(f"Error executing {model}.{method}: {e}")
            raise
    
    def search_read(self, model: str, domain: List = None, fields: List[str] = None, 
                   limit: int = None, offset: int = 0, order: str = None) -> List[Dict]:
        """
        Search and read records from Odoo
        
        Args:
            model: Odoo model name
            domain: Search domain (list of tuples)
            fields: Fields to retrieve
            limit: Maximum number of records
            offset: Number of records to skip
            order: Sort order
            
        Returns:
            List of dictionaries containing record data
        """
        domain = domain or []
        fields = fields or []
        kwargs = {}
        
        if fields:
            kwargs['fields'] = fields
        if limit:
            kwargs['limit'] = limit
        if offset:
            kwargs['offset'] = offset
        if order:
            kwargs['order'] = order
            
        return self.execute(model, 'search_read', domain, kwargs)
    
    def search(self, model: str, domain: List = None, limit: int = None, 
              offset: int = 0, order: str = None) -> List[int]:
        """
        Search for record IDs in Odoo
        
        Args:
            model: Odoo model name
            domain: Search domain
            limit: Maximum number of records
            offset: Number of records to skip
            order: Sort order
            
        Returns:
            List of record IDs
        """
        domain = domain or []
        kwargs = {}
        
        if limit:
            kwargs['limit'] = limit
        if offset:
            kwargs['offset'] = offset
        if order:
            kwargs['order'] = order
            
        return self.execute(model, 'search', domain, kwargs)
    
    def read(self, model: str, ids: List[int], fields: List[str] = None) -> List[Dict]:
        """
        Read records by IDs
        
        Args:
            model: Odoo model name
            ids: List of record IDs
            fields: Fields to retrieve
            
        Returns:
            List of dictionaries containing record data
        """
        kwargs = {}
        if fields:
            kwargs['fields'] = fields
            
        return self.execute(model, 'read', ids, kwargs)
    
    def create(self, model: str, values: Dict) -> int:
        """
        Create a record in Odoo
        
        Args:
            model: Odoo model name
            values: Dictionary of field values
            
        Returns:
            ID of the created record
        """
        return self.execute(model, 'create', [values])
    
    def write(self, model: str, ids: List[int], values: Dict) -> bool:
        """
        Update records in Odoo
        
        Args:
            model: Odoo model name
            ids: List of record IDs to update
            values: Dictionary of field values to update
            
        Returns:
            True if successful
        """
        return self.execute(model, 'write', ids, values)
    
    def unlink(self, model: str, ids: List[int]) -> bool:
        """
        Delete records from Odoo
        
        Args:
            model: Odoo model name
            ids: List of record IDs to delete
            
        Returns:
            True if successful
        """
        return self.execute(model, 'unlink', ids)
    
    def get_model_fields(self, model: str) -> Dict:
        """
        Get field definitions for a model
        
        Args:
            model: Odoo model name
            
        Returns:
            Dictionary of field definitions
        """
        return self.execute(model, 'fields_get')
    
    def call_method(self, model: str, method: str, record_ids: List[int], *args) -> Any:
        """
        Call a custom method on records
        
        Args:
            model: Odoo model name
            method: Method name to call
            record_ids: List of record IDs
            *args: Additional arguments for the method
            
        Returns:
            Result of the method call
        """
        return self.execute(model, method, record_ids, *args)

# Singleton instance
_odoo_connector = None

def get_odoo_connector() -> OdooConnector:
    """
    Get singleton instance of OdooConnector
    """
    global _odoo_connector
    if _odoo_connector is None:
        _odoo_connector = OdooConnector()
    return _odoo_connector
