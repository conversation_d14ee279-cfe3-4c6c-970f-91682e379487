{"ast": null, "code": "var _jsxFileName = \"D:\\\\odoo_erp\\\\react_frontend\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [invoices, setInvoices] = useState([]);\n  const [accounts, setAccounts] = useState([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState(null);\n  useEffect(() => {\n    fetchData();\n  }, []);\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch invoices\n      const invoicesResponse = await fetch('http://localhost:8000/api/accounting/invoices/');\n      if (!invoicesResponse.ok) {\n        throw new Error('Failed to fetch invoices');\n      }\n      const invoicesData = await invoicesResponse.json();\n      setInvoices(invoicesData.results || invoicesData);\n\n      // Fetch accounts\n      const accountsResponse = await fetch('http://localhost:8000/api/accounting/accounts/');\n      if (!accountsResponse.ok) {\n        throw new Error('Failed to fetch accounts');\n      }\n      const accountsData = await accountsResponse.json();\n      setAccounts(accountsData.results || accountsData);\n      setLoading(false);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n      setLoading(false);\n    }\n  };\n  const getInvoiceTypeLabel = moveType => {\n    switch (moveType) {\n      case 'out_invoice':\n        return 'Customer Invoice';\n      case 'in_invoice':\n        return 'Vendor Bill';\n      case 'out_refund':\n        return 'Customer Credit Note';\n      case 'in_refund':\n        return 'Vendor Credit Note';\n      default:\n        return moveType;\n    }\n  };\n  const getStateColor = state => {\n    switch (state) {\n      case 'draft':\n        return '#ffa500';\n      case 'posted':\n        return '#28a745';\n      case 'cancel':\n        return '#dc3545';\n      default:\n        return '#6c757d';\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '50px',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Loading Hybrid ERP System...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: \"Fetching data from Django API...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this);\n  }\n  if (error) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"App\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '50px',\n          textAlign: 'center',\n          color: 'red'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"Error\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: fetchData,\n          children: \"Retry\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 91,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      style: {\n        background: '#282c34',\n        padding: '20px',\n        color: 'white',\n        marginBottom: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        children: \"\\uD83C\\uDFE2 Hybrid ERP System\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 109,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"React Frontend + Django API + PostgreSQL Database\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 110,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 103,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      style: {\n        padding: '0 20px',\n        maxWidth: '1200px',\n        margin: '0 auto'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '20px',\n          marginBottom: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCCA Total Accounts\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2em',\n              color: '#007bff'\n            },\n            children: accounts.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83E\\uDDFE Total Invoices\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 137,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2em',\n              color: '#28a745'\n            },\n            children: invoices.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 131,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n            children: \"\\uD83D\\uDCB0 Total Revenue\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '2em',\n              color: '#ffc107'\n            },\n            children: [\"$\", invoices.filter(inv => inv.move_type === 'out_invoice').reduce((sum, inv) => sum + parseFloat(inv.amount_total), 0).toFixed(2)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 141,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 115,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDCCB Recent Invoices\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 159,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            background: 'white',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6',\n            overflow: 'hidden'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"table\", {\n            style: {\n              width: '100%',\n              borderCollapse: 'collapse'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n              style: {\n                background: '#f8f9fa'\n              },\n              children: /*#__PURE__*/_jsxDEV(\"tr\", {\n                children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '12px',\n                    textAlign: 'left',\n                    borderBottom: '1px solid #dee2e6'\n                  },\n                  children: \"Invoice #\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 169,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '12px',\n                    textAlign: 'left',\n                    borderBottom: '1px solid #dee2e6'\n                  },\n                  children: \"Customer/Vendor\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '12px',\n                    textAlign: 'left',\n                    borderBottom: '1px solid #dee2e6'\n                  },\n                  children: \"Type\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 171,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '12px',\n                    textAlign: 'left',\n                    borderBottom: '1px solid #dee2e6'\n                  },\n                  children: \"Date\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 172,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '12px',\n                    textAlign: 'right',\n                    borderBottom: '1px solid #dee2e6'\n                  },\n                  children: \"Amount\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                  style: {\n                    padding: '12px',\n                    textAlign: 'center',\n                    borderBottom: '1px solid #dee2e6'\n                  },\n                  children: \"Status\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n              children: invoices.map(invoice => /*#__PURE__*/_jsxDEV(\"tr\", {\n                style: {\n                  borderBottom: '1px solid #f8f9fa'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px'\n                  },\n                  children: invoice.ref || invoice.name || `#${invoice.id}`\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 180,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px'\n                  },\n                  children: invoice.partner_name\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 181,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px'\n                  },\n                  children: getInvoiceTypeLabel(invoice.move_type)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 182,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px'\n                  },\n                  children: new Date(invoice.invoice_date).toLocaleDateString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 183,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px',\n                    textAlign: 'right',\n                    fontWeight: 'bold'\n                  },\n                  children: [\"$\", parseFloat(invoice.amount_total).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                  style: {\n                    padding: '12px',\n                    textAlign: 'center'\n                  },\n                  children: /*#__PURE__*/_jsxDEV(\"span\", {\n                    style: {\n                      padding: '4px 8px',\n                      borderRadius: '4px',\n                      color: 'white',\n                      fontSize: '0.8em',\n                      textTransform: 'uppercase',\n                      backgroundColor: getStateColor(invoice.state)\n                    },\n                    children: invoice.state\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 188,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 187,\n                  columnNumber: 21\n                }, this)]\n              }, invoice.id, true, {\n                fileName: _jsxFileName,\n                lineNumber: 179,\n                columnNumber: 19\n              }, this))\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 158,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '30px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          children: \"\\uD83D\\uDCCA Chart of Accounts\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '15px'\n          },\n          children: accounts.map(account => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              background: 'white',\n              padding: '15px',\n              borderRadius: '8px',\n              border: '1px solid #dee2e6',\n              boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                marginBottom: '8px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"strong\", {\n                style: {\n                  color: '#007bff'\n                },\n                children: account.code\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 228,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  padding: '2px 6px',\n                  borderRadius: '3px',\n                  fontSize: '0.7em',\n                  backgroundColor: '#e9ecef',\n                  color: '#495057'\n                },\n                children: account.account_type.replace('_', ' ').toUpperCase()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 222,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#495057'\n              },\n              children: account.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 239,\n              columnNumber: 17\n            }, this)]\n          }, account.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 215,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 209,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 207,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          background: '#d4edda',\n          border: '1px solid #c3e6cb',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          style: {\n            color: '#155724',\n            margin: '0 0 10px 0'\n          },\n          children: \"\\u2705 System Status\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 253,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            color: '#155724'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u2705 React Frontend: Connected and displaying data\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u2705 Django API: Running at http://localhost:8000\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"\\u2705 PostgreSQL Database: Connected with \", accounts.length + invoices.length, \" records\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: \"\\u2705 Hybrid Architecture: Fully operational\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 258,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 254,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 246,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"footer\", {\n      style: {\n        textAlign: 'center',\n        padding: '20px',\n        color: '#6c757d',\n        borderTop: '1px solid #dee2e6',\n        marginTop: '40px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"\\uD83D\\uDE80 Hybrid ERP System - React + Django + PostgreSQL\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 270,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n        children: \"Ready for customization and business-specific features\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 271,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 102,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"zu/u1VBab/w5VtAuHO9h+Ks/hO4=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "App", "_s", "invoices", "setInvoices", "accounts", "setAccounts", "loading", "setLoading", "error", "setError", "fetchData", "invoicesResponse", "fetch", "ok", "Error", "invoicesData", "json", "results", "accountsResponse", "accountsData", "err", "message", "getInvoiceTypeLabel", "moveType", "getStateColor", "state", "className", "children", "style", "padding", "textAlign", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "onClick", "background", "marginBottom", "max<PERSON><PERSON><PERSON>", "margin", "display", "gridTemplateColumns", "gap", "borderRadius", "border", "fontSize", "length", "filter", "inv", "move_type", "reduce", "sum", "parseFloat", "amount_total", "toFixed", "overflow", "width", "borderCollapse", "borderBottom", "map", "invoice", "ref", "name", "id", "partner_name", "Date", "invoice_date", "toLocaleDateString", "fontWeight", "textTransform", "backgroundColor", "account", "boxShadow", "justifyContent", "alignItems", "code", "account_type", "replace", "toUpperCase", "borderTop", "marginTop", "_c", "$RefreshReg$"], "sources": ["D:/odoo_erp/react_frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport './App.css';\n\ninterface Invoice {\n  id: number;\n  name: string;\n  partner_name: string;\n  move_type: string;\n  state: string;\n  invoice_date: string;\n  amount_total: string;\n  ref: string;\n}\n\ninterface Account {\n  id: number;\n  code: string;\n  name: string;\n  account_type: string;\n}\n\nfunction App() {\n  const [invoices, setInvoices] = useState<Invoice[]>([]);\n  const [accounts, setAccounts] = useState<Account[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchData();\n  }, []);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n\n      // Fetch invoices\n      const invoicesResponse = await fetch('http://localhost:8000/api/accounting/invoices/');\n      if (!invoicesResponse.ok) {\n        throw new Error('Failed to fetch invoices');\n      }\n      const invoicesData = await invoicesResponse.json();\n      setInvoices(invoicesData.results || invoicesData);\n\n      // Fetch accounts\n      const accountsResponse = await fetch('http://localhost:8000/api/accounting/accounts/');\n      if (!accountsResponse.ok) {\n        throw new Error('Failed to fetch accounts');\n      }\n      const accountsData = await accountsResponse.json();\n      setAccounts(accountsData.results || accountsData);\n\n      setLoading(false);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An error occurred');\n      setLoading(false);\n    }\n  };\n\n  const getInvoiceTypeLabel = (moveType: string) => {\n    switch (moveType) {\n      case 'out_invoice': return 'Customer Invoice';\n      case 'in_invoice': return 'Vendor Bill';\n      case 'out_refund': return 'Customer Credit Note';\n      case 'in_refund': return 'Vendor Credit Note';\n      default: return moveType;\n    }\n  };\n\n  const getStateColor = (state: string) => {\n    switch (state) {\n      case 'draft': return '#ffa500';\n      case 'posted': return '#28a745';\n      case 'cancel': return '#dc3545';\n      default: return '#6c757d';\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"App\">\n        <div style={{ padding: '50px', textAlign: 'center' }}>\n          <h2>Loading Hybrid ERP System...</h2>\n          <div>Fetching data from Django API...</div>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"App\">\n        <div style={{ padding: '50px', textAlign: 'center', color: 'red' }}>\n          <h2>Error</h2>\n          <p>{error}</p>\n          <button onClick={fetchData}>Retry</button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"App\">\n      <header style={{\n        background: '#282c34',\n        padding: '20px',\n        color: 'white',\n        marginBottom: '20px'\n      }}>\n        <h1>🏢 Hybrid ERP System</h1>\n        <p>React Frontend + Django API + PostgreSQL Database</p>\n      </header>\n\n      <main style={{ padding: '0 20px', maxWidth: '1200px', margin: '0 auto' }}>\n        {/* Dashboard Summary */}\n        <div style={{\n          display: 'grid',\n          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',\n          gap: '20px',\n          marginBottom: '30px'\n        }}>\n          <div style={{\n            background: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          }}>\n            <h3>📊 Total Accounts</h3>\n            <div style={{ fontSize: '2em', color: '#007bff' }}>{accounts.length}</div>\n          </div>\n\n          <div style={{\n            background: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          }}>\n            <h3>🧾 Total Invoices</h3>\n            <div style={{ fontSize: '2em', color: '#28a745' }}>{invoices.length}</div>\n          </div>\n\n          <div style={{\n            background: '#f8f9fa',\n            padding: '20px',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6'\n          }}>\n            <h3>💰 Total Revenue</h3>\n            <div style={{ fontSize: '2em', color: '#ffc107' }}>\n              ${invoices\n                .filter(inv => inv.move_type === 'out_invoice')\n                .reduce((sum, inv) => sum + parseFloat(inv.amount_total), 0)\n                .toFixed(2)}\n            </div>\n          </div>\n        </div>\n\n        {/* Recent Invoices */}\n        <div style={{ marginBottom: '30px' }}>\n          <h2>📋 Recent Invoices</h2>\n          <div style={{\n            background: 'white',\n            borderRadius: '8px',\n            border: '1px solid #dee2e6',\n            overflow: 'hidden'\n          }}>\n            <table style={{ width: '100%', borderCollapse: 'collapse' }}>\n              <thead style={{ background: '#f8f9fa' }}>\n                <tr>\n                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>Invoice #</th>\n                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>Customer/Vendor</th>\n                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>Type</th>\n                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>Date</th>\n                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>Amount</th>\n                  <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #dee2e6' }}>Status</th>\n                </tr>\n              </thead>\n              <tbody>\n                {invoices.map((invoice) => (\n                  <tr key={invoice.id} style={{ borderBottom: '1px solid #f8f9fa' }}>\n                    <td style={{ padding: '12px' }}>{invoice.ref || invoice.name || `#${invoice.id}`}</td>\n                    <td style={{ padding: '12px' }}>{invoice.partner_name}</td>\n                    <td style={{ padding: '12px' }}>{getInvoiceTypeLabel(invoice.move_type)}</td>\n                    <td style={{ padding: '12px' }}>{new Date(invoice.invoice_date).toLocaleDateString()}</td>\n                    <td style={{ padding: '12px', textAlign: 'right', fontWeight: 'bold' }}>\n                      ${parseFloat(invoice.amount_total).toFixed(2)}\n                    </td>\n                    <td style={{ padding: '12px', textAlign: 'center' }}>\n                      <span style={{\n                        padding: '4px 8px',\n                        borderRadius: '4px',\n                        color: 'white',\n                        fontSize: '0.8em',\n                        textTransform: 'uppercase',\n                        backgroundColor: getStateColor(invoice.state)\n                      }}>\n                        {invoice.state}\n                      </span>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n\n        {/* Chart of Accounts */}\n        <div style={{ marginBottom: '30px' }}>\n          <h2>📊 Chart of Accounts</h2>\n          <div style={{\n            display: 'grid',\n            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',\n            gap: '15px'\n          }}>\n            {accounts.map((account) => (\n              <div key={account.id} style={{\n                background: 'white',\n                padding: '15px',\n                borderRadius: '8px',\n                border: '1px solid #dee2e6',\n                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'\n              }}>\n                <div style={{\n                  display: 'flex',\n                  justifyContent: 'space-between',\n                  alignItems: 'center',\n                  marginBottom: '8px'\n                }}>\n                  <strong style={{ color: '#007bff' }}>{account.code}</strong>\n                  <span style={{\n                    padding: '2px 6px',\n                    borderRadius: '3px',\n                    fontSize: '0.7em',\n                    backgroundColor: '#e9ecef',\n                    color: '#495057'\n                  }}>\n                    {account.account_type.replace('_', ' ').toUpperCase()}\n                  </span>\n                </div>\n                <div style={{ color: '#495057' }}>{account.name}</div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* System Status */}\n        <div style={{\n          background: '#d4edda',\n          border: '1px solid #c3e6cb',\n          borderRadius: '8px',\n          padding: '15px',\n          marginBottom: '20px'\n        }}>\n          <h3 style={{ color: '#155724', margin: '0 0 10px 0' }}>✅ System Status</h3>\n          <div style={{ color: '#155724' }}>\n            <div>✅ React Frontend: Connected and displaying data</div>\n            <div>✅ Django API: Running at http://localhost:8000</div>\n            <div>✅ PostgreSQL Database: Connected with {accounts.length + invoices.length} records</div>\n            <div>✅ Hybrid Architecture: Fully operational</div>\n          </div>\n        </div>\n      </main>\n\n      <footer style={{\n        textAlign: 'center',\n        padding: '20px',\n        color: '#6c757d',\n        borderTop: '1px solid #dee2e6',\n        marginTop: '40px'\n      }}>\n        <p>🚀 Hybrid ERP System - React + Django + PostgreSQL</p>\n        <p>Ready for customization and business-specific features</p>\n      </footer>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAoBnB,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGP,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACQ,QAAQ,EAAEC,WAAW,CAAC,GAAGT,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACU,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,IAAI,CAAC;EAC5C,MAAM,CAACY,KAAK,EAAEC,QAAQ,CAAC,GAAGb,QAAQ,CAAgB,IAAI,CAAC;EAEvDC,SAAS,CAAC,MAAM;IACda,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,IAAI;MACFH,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,MAAMI,gBAAgB,GAAG,MAAMC,KAAK,CAAC,gDAAgD,CAAC;MACtF,IAAI,CAACD,gBAAgB,CAACE,EAAE,EAAE;QACxB,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MACA,MAAMC,YAAY,GAAG,MAAMJ,gBAAgB,CAACK,IAAI,CAAC,CAAC;MAClDb,WAAW,CAACY,YAAY,CAACE,OAAO,IAAIF,YAAY,CAAC;;MAEjD;MACA,MAAMG,gBAAgB,GAAG,MAAMN,KAAK,CAAC,gDAAgD,CAAC;MACtF,IAAI,CAACM,gBAAgB,CAACL,EAAE,EAAE;QACxB,MAAM,IAAIC,KAAK,CAAC,0BAA0B,CAAC;MAC7C;MACA,MAAMK,YAAY,GAAG,MAAMD,gBAAgB,CAACF,IAAI,CAAC,CAAC;MAClDX,WAAW,CAACc,YAAY,CAACF,OAAO,IAAIE,YAAY,CAAC;MAEjDZ,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,CAAC,OAAOa,GAAG,EAAE;MACZX,QAAQ,CAACW,GAAG,YAAYN,KAAK,GAAGM,GAAG,CAACC,OAAO,GAAG,mBAAmB,CAAC;MAClEd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMe,mBAAmB,GAAIC,QAAgB,IAAK;IAChD,QAAQA,QAAQ;MACd,KAAK,aAAa;QAAE,OAAO,kBAAkB;MAC7C,KAAK,YAAY;QAAE,OAAO,aAAa;MACvC,KAAK,YAAY;QAAE,OAAO,sBAAsB;MAChD,KAAK,WAAW;QAAE,OAAO,oBAAoB;MAC7C;QAAS,OAAOA,QAAQ;IAC1B;EACF,CAAC;EAED,MAAMC,aAAa,GAAIC,KAAa,IAAK;IACvC,QAAQA,KAAK;MACX,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,IAAInB,OAAO,EAAE;IACX,oBACEP,OAAA;MAAK2B,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClB5B,OAAA;QAAK6B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,SAAS,EAAE;QAAS,CAAE;QAAAH,QAAA,gBACnD5B,OAAA;UAAA4B,QAAA,EAAI;QAA4B;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACrCnC,OAAA;UAAA4B,QAAA,EAAK;QAAgC;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACxC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,IAAI1B,KAAK,EAAE;IACT,oBACET,OAAA;MAAK2B,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClB5B,OAAA;QAAK6B,KAAK,EAAE;UAAEC,OAAO,EAAE,MAAM;UAAEC,SAAS,EAAE,QAAQ;UAAEK,KAAK,EAAE;QAAM,CAAE;QAAAR,QAAA,gBACjE5B,OAAA;UAAA4B,QAAA,EAAI;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdnC,OAAA;UAAA4B,QAAA,EAAInB;QAAK;UAAAuB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACdnC,OAAA;UAAQqC,OAAO,EAAE1B,SAAU;UAAAiB,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV;EAEA,oBACEnC,OAAA;IAAK2B,SAAS,EAAC,KAAK;IAAAC,QAAA,gBAClB5B,OAAA;MAAQ6B,KAAK,EAAE;QACbS,UAAU,EAAE,SAAS;QACrBR,OAAO,EAAE,MAAM;QACfM,KAAK,EAAE,OAAO;QACdG,YAAY,EAAE;MAChB,CAAE;MAAAX,QAAA,gBACA5B,OAAA;QAAA4B,QAAA,EAAI;MAAoB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAC7BnC,OAAA;QAAA4B,QAAA,EAAG;MAAiD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClD,CAAC,eAETnC,OAAA;MAAM6B,KAAK,EAAE;QAAEC,OAAO,EAAE,QAAQ;QAAEU,QAAQ,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAS,CAAE;MAAAb,QAAA,gBAEvE5B,OAAA;QAAK6B,KAAK,EAAE;UACVa,OAAO,EAAE,MAAM;UACfC,mBAAmB,EAAE,sCAAsC;UAC3DC,GAAG,EAAE,MAAM;UACXL,YAAY,EAAE;QAChB,CAAE;QAAAX,QAAA,gBACA5B,OAAA;UAAK6B,KAAK,EAAE;YACVS,UAAU,EAAE,SAAS;YACrBR,OAAO,EAAE,MAAM;YACfe,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,gBACA5B,OAAA;YAAA4B,QAAA,EAAI;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BnC,OAAA;YAAK6B,KAAK,EAAE;cAAEkB,QAAQ,EAAE,KAAK;cAAEX,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAEvB,QAAQ,CAAC2C;UAAM;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAENnC,OAAA;UAAK6B,KAAK,EAAE;YACVS,UAAU,EAAE,SAAS;YACrBR,OAAO,EAAE,MAAM;YACfe,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,gBACA5B,OAAA;YAAA4B,QAAA,EAAI;UAAiB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC1BnC,OAAA;YAAK6B,KAAK,EAAE;cAAEkB,QAAQ,EAAE,KAAK;cAAEX,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,EAAEzB,QAAQ,CAAC6C;UAAM;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvE,CAAC,eAENnC,OAAA;UAAK6B,KAAK,EAAE;YACVS,UAAU,EAAE,SAAS;YACrBR,OAAO,EAAE,MAAM;YACfe,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE;UACV,CAAE;UAAAlB,QAAA,gBACA5B,OAAA;YAAA4B,QAAA,EAAI;UAAgB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzBnC,OAAA;YAAK6B,KAAK,EAAE;cAAEkB,QAAQ,EAAE,KAAK;cAAEX,KAAK,EAAE;YAAU,CAAE;YAAAR,QAAA,GAAC,GAChD,EAACzB,QAAQ,CACP8C,MAAM,CAACC,GAAG,IAAIA,GAAG,CAACC,SAAS,KAAK,aAAa,CAAC,CAC9CC,MAAM,CAAC,CAACC,GAAG,EAAEH,GAAG,KAAKG,GAAG,GAAGC,UAAU,CAACJ,GAAG,CAACK,YAAY,CAAC,EAAE,CAAC,CAAC,CAC3DC,OAAO,CAAC,CAAC,CAAC;UAAA;YAAAxB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK6B,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACnC5B,OAAA;UAAA4B,QAAA,EAAI;QAAkB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3BnC,OAAA;UAAK6B,KAAK,EAAE;YACVS,UAAU,EAAE,OAAO;YACnBO,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,mBAAmB;YAC3BW,QAAQ,EAAE;UACZ,CAAE;UAAA7B,QAAA,eACA5B,OAAA;YAAO6B,KAAK,EAAE;cAAE6B,KAAK,EAAE,MAAM;cAAEC,cAAc,EAAE;YAAW,CAAE;YAAA/B,QAAA,gBAC1D5B,OAAA;cAAO6B,KAAK,EAAE;gBAAES,UAAU,EAAE;cAAU,CAAE;cAAAV,QAAA,eACtC5B,OAAA;gBAAA4B,QAAA,gBACE5B,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,MAAM;oBAAE6B,YAAY,EAAE;kBAAoB,CAAE;kBAAAhC,QAAA,EAAC;gBAAS;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACpGnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,MAAM;oBAAE6B,YAAY,EAAE;kBAAoB,CAAE;kBAAAhC,QAAA,EAAC;gBAAe;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC1GnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,MAAM;oBAAE6B,YAAY,EAAE;kBAAoB,CAAE;kBAAAhC,QAAA,EAAC;gBAAI;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/FnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,MAAM;oBAAE6B,YAAY,EAAE;kBAAoB,CAAE;kBAAAhC,QAAA,EAAC;gBAAI;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC/FnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,OAAO;oBAAE6B,YAAY,EAAE;kBAAoB,CAAE;kBAAAhC,QAAA,EAAC;gBAAM;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClGnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,QAAQ;oBAAE6B,YAAY,EAAE;kBAAoB,CAAE;kBAAAhC,QAAA,EAAC;gBAAM;kBAAAI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eACRnC,OAAA;cAAA4B,QAAA,EACGzB,QAAQ,CAAC0D,GAAG,CAAEC,OAAO,iBACpB9D,OAAA;gBAAqB6B,KAAK,EAAE;kBAAE+B,YAAY,EAAE;gBAAoB,CAAE;gBAAAhC,QAAA,gBAChE5B,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAEkC,OAAO,CAACC,GAAG,IAAID,OAAO,CAACE,IAAI,IAAI,IAAIF,OAAO,CAACG,EAAE;gBAAE;kBAAAjC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACtFnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAEkC,OAAO,CAACI;gBAAY;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC3DnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAEL,mBAAmB,CAACuC,OAAO,CAACX,SAAS;gBAAC;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC7EnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE;kBAAO,CAAE;kBAAAF,QAAA,EAAE,IAAIuC,IAAI,CAACL,OAAO,CAACM,YAAY,CAAC,CAACC,kBAAkB,CAAC;gBAAC;kBAAArC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eAC1FnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE,OAAO;oBAAEuC,UAAU,EAAE;kBAAO,CAAE;kBAAA1C,QAAA,GAAC,GACrE,EAAC0B,UAAU,CAACQ,OAAO,CAACP,YAAY,CAAC,CAACC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAxB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C,CAAC,eACLnC,OAAA;kBAAI6B,KAAK,EAAE;oBAAEC,OAAO,EAAE,MAAM;oBAAEC,SAAS,EAAE;kBAAS,CAAE;kBAAAH,QAAA,eAClD5B,OAAA;oBAAM6B,KAAK,EAAE;sBACXC,OAAO,EAAE,SAAS;sBAClBe,YAAY,EAAE,KAAK;sBACnBT,KAAK,EAAE,OAAO;sBACdW,QAAQ,EAAE,OAAO;sBACjBwB,aAAa,EAAE,WAAW;sBAC1BC,eAAe,EAAE/C,aAAa,CAACqC,OAAO,CAACpC,KAAK;oBAC9C,CAAE;oBAAAE,QAAA,EACCkC,OAAO,CAACpC;kBAAK;oBAAAM,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACV;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACL,CAAC;cAAA,GAnBE2B,OAAO,CAACG,EAAE;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAoBf,CACL;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK6B,KAAK,EAAE;UAAEU,YAAY,EAAE;QAAO,CAAE;QAAAX,QAAA,gBACnC5B,OAAA;UAAA4B,QAAA,EAAI;QAAoB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC7BnC,OAAA;UAAK6B,KAAK,EAAE;YACVa,OAAO,EAAE,MAAM;YACfC,mBAAmB,EAAE,sCAAsC;YAC3DC,GAAG,EAAE;UACP,CAAE;UAAAhB,QAAA,EACCvB,QAAQ,CAACwD,GAAG,CAAEY,OAAO,iBACpBzE,OAAA;YAAsB6B,KAAK,EAAE;cAC3BS,UAAU,EAAE,OAAO;cACnBR,OAAO,EAAE,MAAM;cACfe,YAAY,EAAE,KAAK;cACnBC,MAAM,EAAE,mBAAmB;cAC3B4B,SAAS,EAAE;YACb,CAAE;YAAA9C,QAAA,gBACA5B,OAAA;cAAK6B,KAAK,EAAE;gBACVa,OAAO,EAAE,MAAM;gBACfiC,cAAc,EAAE,eAAe;gBAC/BC,UAAU,EAAE,QAAQ;gBACpBrC,YAAY,EAAE;cAChB,CAAE;cAAAX,QAAA,gBACA5B,OAAA;gBAAQ6B,KAAK,EAAE;kBAAEO,KAAK,EAAE;gBAAU,CAAE;gBAAAR,QAAA,EAAE6C,OAAO,CAACI;cAAI;gBAAA7C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAS,CAAC,eAC5DnC,OAAA;gBAAM6B,KAAK,EAAE;kBACXC,OAAO,EAAE,SAAS;kBAClBe,YAAY,EAAE,KAAK;kBACnBE,QAAQ,EAAE,OAAO;kBACjByB,eAAe,EAAE,SAAS;kBAC1BpC,KAAK,EAAE;gBACT,CAAE;gBAAAR,QAAA,EACC6C,OAAO,CAACK,YAAY,CAACC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC;cAAC;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACNnC,OAAA;cAAK6B,KAAK,EAAE;gBAAEO,KAAK,EAAE;cAAU,CAAE;cAAAR,QAAA,EAAE6C,OAAO,CAACT;YAAI;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA,GAxB9CsC,OAAO,CAACR,EAAE;YAAAjC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAyBf,CACN;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNnC,OAAA;QAAK6B,KAAK,EAAE;UACVS,UAAU,EAAE,SAAS;UACrBQ,MAAM,EAAE,mBAAmB;UAC3BD,YAAY,EAAE,KAAK;UACnBf,OAAO,EAAE,MAAM;UACfS,YAAY,EAAE;QAChB,CAAE;QAAAX,QAAA,gBACA5B,OAAA;UAAI6B,KAAK,EAAE;YAAEO,KAAK,EAAE,SAAS;YAAEK,MAAM,EAAE;UAAa,CAAE;UAAAb,QAAA,EAAC;QAAe;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC3EnC,OAAA;UAAK6B,KAAK,EAAE;YAAEO,KAAK,EAAE;UAAU,CAAE;UAAAR,QAAA,gBAC/B5B,OAAA;YAAA4B,QAAA,EAAK;UAA+C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC1DnC,OAAA;YAAA4B,QAAA,EAAK;UAA8C;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACzDnC,OAAA;YAAA4B,QAAA,GAAK,6CAAsC,EAACvB,QAAQ,CAAC2C,MAAM,GAAG7C,QAAQ,CAAC6C,MAAM,EAAC,UAAQ;UAAA;YAAAhB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC5FnC,OAAA;YAAA4B,QAAA,EAAK;UAAwC;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAEPnC,OAAA;MAAQ6B,KAAK,EAAE;QACbE,SAAS,EAAE,QAAQ;QACnBD,OAAO,EAAE,MAAM;QACfM,KAAK,EAAE,SAAS;QAChB6C,SAAS,EAAE,mBAAmB;QAC9BC,SAAS,EAAE;MACb,CAAE;MAAAtD,QAAA,gBACA5B,OAAA;QAAA4B,QAAA,EAAG;MAAkD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC,eACzDnC,OAAA;QAAA4B,QAAA,EAAG;MAAsD;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACjC,EAAA,CA7PQD,GAAG;AAAAkF,EAAA,GAAHlF,GAAG;AA+PZ,eAAeA,GAAG;AAAC,IAAAkF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}