#!/usr/bin/env python3
"""
Odoo Community Edition Setup Script
This script helps set up Odoo Community Edition with proper configuration
"""

import os
import sys
import subprocess
import urllib.request
import zipfile
import shutil
from pathlib import Path

def run_command(command, cwd=None):
    """Run a command and return the result"""
    try:
        result = subprocess.run(command, shell=True, cwd=cwd, 
                              capture_output=True, text=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {command}")
        print(f"Error: {e.stderr}")
        return None

def check_prerequisites():
    """Check if all prerequisites are installed"""
    print("🔍 Checking prerequisites...")
    
    # Check Python
    python_version = run_command("python --version")
    if python_version:
        print(f"✅ Python: {python_version}")
    else:
        print("❌ Python not found")
        return False
    
    # Check PostgreSQL
    pg_version = run_command("psql --version")
    if pg_version:
        print(f"✅ PostgreSQL: {pg_version}")
    else:
        print("❌ PostgreSQL not found")
        print("Please install PostgreSQL from https://www.postgresql.org/download/windows/")
        return False
    
    # Check Git
    git_version = run_command("git --version")
    if git_version:
        print(f"✅ Git: {git_version}")
    else:
        print("❌ Git not found")
        return False
    
    return True

def download_odoo():
    """Download Odoo Community Edition"""
    print("📥 Downloading Odoo Community Edition...")
    
    if os.path.exists("odoo"):
        print("Odoo directory already exists. Skipping download.")
        return True
    
    # Clone Odoo 17.0 (latest stable)
    result = run_command("git clone https://github.com/odoo/odoo.git --depth 1 --branch 17.0")
    if result is not None:
        print("✅ Odoo downloaded successfully")
        return True
    else:
        print("❌ Failed to download Odoo")
        return False

def install_python_dependencies():
    """Install Python dependencies for Odoo"""
    print("📦 Installing Python dependencies...")
    
    # Install requirements
    result = run_command("pip install -r odoo/requirements.txt")
    if result is None:
        print("❌ Failed to install requirements")
        return False
    
    # Install additional Windows-specific dependencies
    additional_deps = [
        "psycopg2-binary",
        "python-ldap-win",
        "Pillow",
        "lxml",
        "reportlab",
        "PyPDF2"
    ]
    
    for dep in additional_deps:
        print(f"Installing {dep}...")
        result = run_command(f"pip install {dep}")
        if result is None:
            print(f"⚠️ Warning: Failed to install {dep}")
    
    print("✅ Python dependencies installed")
    return True

def create_odoo_config():
    """Create Odoo configuration file"""
    print("⚙️ Creating Odoo configuration...")
    
    config_content = """[options]
; Admin password for database management
admin_passwd = admin123

; Database settings
db_host = localhost
db_port = 5432
db_user = odoo
db_password = odoo

; Server settings
http_port = 8069
workers = 0

; Paths
addons_path = odoo/addons,odoo/odoo/addons
data_dir = data

; Logging
log_level = info
log_handler = :INFO

; Security
list_db = True
"""
    
    with open("odoo.conf", "w") as f:
        f.write(config_content)
    
    print("✅ Configuration file created: odoo.conf")
    return True

def create_database_setup():
    """Create database setup script"""
    print("🗄️ Creating database setup script...")
    
    db_script = """-- Run this script in PostgreSQL to set up Odoo database
-- Connect to PostgreSQL as superuser (postgres) and run:

-- Create Odoo user
CREATE USER odoo WITH CREATEDB PASSWORD 'odoo';

-- Create database
CREATE DATABASE odoo_erp OWNER odoo;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE odoo_erp TO odoo;
"""
    
    with open("setup_database.sql", "w") as f:
        f.write(db_script)
    
    print("✅ Database setup script created: setup_database.sql")
    return True

def create_startup_script():
    """Create startup script for Odoo"""
    print("🚀 Creating startup script...")
    
    startup_script = """@echo off
echo Starting Odoo Community Edition...
echo.
echo Make sure PostgreSQL is running and database is set up.
echo.
python odoo/odoo-bin -c odoo.conf
pause
"""
    
    with open("start_odoo.bat", "w") as f:
        f.write(startup_script)
    
    print("✅ Startup script created: start_odoo.bat")
    return True

def main():
    """Main setup function"""
    print("🎯 Odoo Community Edition Setup")
    print("=" * 40)
    
    # Check prerequisites
    if not check_prerequisites():
        print("\n❌ Prerequisites check failed. Please install missing components.")
        sys.exit(1)
    
    # Download Odoo
    if not download_odoo():
        print("\n❌ Failed to download Odoo")
        sys.exit(1)
    
    # Install dependencies
    if not install_python_dependencies():
        print("\n❌ Failed to install dependencies")
        sys.exit(1)
    
    # Create configuration
    create_odoo_config()
    create_database_setup()
    create_startup_script()
    
    # Create data directory
    os.makedirs("data", exist_ok=True)
    
    print("\n🎉 Odoo setup completed successfully!")
    print("\nNext steps:")
    print("1. Set up PostgreSQL database:")
    print("   - Connect to PostgreSQL as superuser")
    print("   - Run the commands in setup_database.sql")
    print("2. Start Odoo by running: start_odoo.bat")
    print("3. Open browser and go to: http://localhost:8069")
    print("4. Create your first database and install accounting module")

if __name__ == "__main__":
    main()
