import React, { useState, useEffect } from 'react';
import './App.css';

interface Invoice {
  id: number;
  name: string;
  partner_name: string;
  move_type: string;
  state: string;
  invoice_date: string;
  amount_total: string;
  ref: string;
}

interface Account {
  id: number;
  code: string;
  name: string;
  account_type: string;
}

function App() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch invoices
      const invoicesResponse = await fetch('http://localhost:8000/api/accounting/invoices/');
      if (!invoicesResponse.ok) {
        throw new Error('Failed to fetch invoices');
      }
      const invoicesData = await invoicesResponse.json();
      setInvoices(invoicesData.results || invoicesData);

      // Fetch accounts
      const accountsResponse = await fetch('http://localhost:8000/api/accounting/accounts/');
      if (!accountsResponse.ok) {
        throw new Error('Failed to fetch accounts');
      }
      const accountsData = await accountsResponse.json();
      setAccounts(accountsData.results || accountsData);

      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="App">
        <div style={{ padding: '50px', textAlign: 'center' }}>
          <h2>🔄 Loading Hybrid ERP System...</h2>
          <div>Fetching data from Django API...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="App">
        <div style={{ padding: '50px', textAlign: 'center', color: 'red' }}>
          <h2>❌ Connection Error</h2>
          <p>{error}</p>
          <p>Make sure Django API is running at http://localhost:8000</p>
          <button onClick={fetchData} style={{ padding: '10px 20px', marginTop: '10px' }}>
            🔄 Retry Connection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      <header style={{
        background: '#282c34',
        padding: '20px',
        color: 'white',
        marginBottom: '20px'
      }}>
        <h1>🏢 Hybrid ERP System</h1>
        <p>React Frontend + Django API + PostgreSQL Database</p>
      </header>

      <div style={{ padding: '0 20px', maxWidth: '1200px', margin: '0 auto' }}>
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          <div style={{
            background: '#f8f9fa',
            padding: '20px',
            borderRadius: '8px',
            border: '1px solid #dee2e6'
          }}>
            <h3>📊 Total Accounts</h3>
            <div style={{ fontSize: '2em', color: '#007bff' }}>{accounts.length}</div>
          </div>

          <div style={{
            background: '#f8f9fa',
            padding: '20px',
            borderRadius: '8px',
            border: '1px solid #dee2e6'
          }}>
            <h3>🧾 Total Invoices</h3>
            <div style={{ fontSize: '2em', color: '#28a745' }}>{invoices.length}</div>
          </div>

          <div style={{
            background: '#f8f9fa',
            padding: '20px',
            borderRadius: '8px',
            border: '1px solid #dee2e6'
          }}>
            <h3>💰 Total Revenue</h3>
            <div style={{ fontSize: '2em', color: '#ffc107' }}>
              ${invoices
                .filter(inv => inv.move_type === 'out_invoice')
                .reduce((sum, inv) => sum + parseFloat(inv.amount_total), 0)
                .toFixed(2)}
            </div>
          </div>
        </div>

        <div style={{
          background: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '8px',
          padding: '15px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#155724', margin: '0 0 10px 0' }}>✅ System Status</h3>
          <div style={{ color: '#155724' }}>
            <div>✅ React Frontend: Connected and displaying data</div>
            <div>✅ Django API: Running at http://localhost:8000</div>
            <div>✅ PostgreSQL Database: Connected with {accounts.length + invoices.length} records</div>
            <div>✅ Hybrid Architecture: Fully operational</div>
          </div>
        </div>
      </div>

      <footer style={{
        textAlign: 'center',
        padding: '20px',
        color: '#6c757d',
        borderTop: '1px solid #dee2e6',
        marginTop: '40px'
      }}>
        <p>🚀 Hybrid ERP System - React + Django + PostgreSQL</p>
        <p>Ready for customization and business-specific features</p>
      </footer>
    </div>
  );
}

export default App;
