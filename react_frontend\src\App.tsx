import React, { useState, useEffect } from 'react';
import './App.css';

interface Invoice {
  id: number;
  name: string;
  partner_name: string;
  move_type: string;
  state: string;
  invoice_date: string;
  amount_total: string;
  ref: string;
}

interface Account {
  id: number;
  code: string;
  name: string;
  account_type: string;
}

function App() {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [accounts, setAccounts] = useState<Account[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoading(true);

      // Fetch invoices
      const invoicesResponse = await fetch('http://localhost:8000/api/accounting/invoices/');
      if (!invoicesResponse.ok) {
        throw new Error('Failed to fetch invoices');
      }
      const invoicesData = await invoicesResponse.json();
      setInvoices(invoicesData.results || invoicesData);

      // Fetch accounts
      const accountsResponse = await fetch('http://localhost:8000/api/accounting/accounts/');
      if (!accountsResponse.ok) {
        throw new Error('Failed to fetch accounts');
      }
      const accountsData = await accountsResponse.json();
      setAccounts(accountsData.results || accountsData);

      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      setLoading(false);
    }
  };

  const getInvoiceTypeLabel = (moveType: string) => {
    switch (moveType) {
      case 'out_invoice': return 'Customer Invoice';
      case 'in_invoice': return 'Vendor Bill';
      case 'out_refund': return 'Customer Credit Note';
      case 'in_refund': return 'Vendor Credit Note';
      default: return moveType;
    }
  };

  const getStateColor = (state: string) => {
    switch (state) {
      case 'draft': return '#ffa500';
      case 'posted': return '#28a745';
      case 'cancel': return '#dc3545';
      default: return '#6c757d';
    }
  };

  if (loading) {
    return (
      <div className="App">
        <div style={{ padding: '50px', textAlign: 'center' }}>
          <h2>Loading Hybrid ERP System...</h2>
          <div>Fetching data from Django API...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="App">
        <div style={{ padding: '50px', textAlign: 'center', color: 'red' }}>
          <h2>Error</h2>
          <p>{error}</p>
          <button onClick={fetchData}>Retry</button>
        </div>
      </div>
    );
  }

  return (
    <div className="App">
      <header style={{
        background: '#282c34',
        padding: '20px',
        color: 'white',
        marginBottom: '20px'
      }}>
        <h1>🏢 Hybrid ERP System</h1>
        <p>React Frontend + Django API + PostgreSQL Database</p>
      </header>

      <main style={{ padding: '0 20px', maxWidth: '1200px', margin: '0 auto' }}>
        {/* Dashboard Summary */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
          gap: '20px',
          marginBottom: '30px'
        }}>
          <div style={{
            background: '#f8f9fa',
            padding: '20px',
            borderRadius: '8px',
            border: '1px solid #dee2e6'
          }}>
            <h3>📊 Total Accounts</h3>
            <div style={{ fontSize: '2em', color: '#007bff' }}>{accounts.length}</div>
          </div>

          <div style={{
            background: '#f8f9fa',
            padding: '20px',
            borderRadius: '8px',
            border: '1px solid #dee2e6'
          }}>
            <h3>🧾 Total Invoices</h3>
            <div style={{ fontSize: '2em', color: '#28a745' }}>{invoices.length}</div>
          </div>

          <div style={{
            background: '#f8f9fa',
            padding: '20px',
            borderRadius: '8px',
            border: '1px solid #dee2e6'
          }}>
            <h3>💰 Total Revenue</h3>
            <div style={{ fontSize: '2em', color: '#ffc107' }}>
              ${invoices
                .filter(inv => inv.move_type === 'out_invoice')
                .reduce((sum, inv) => sum + parseFloat(inv.amount_total), 0)
                .toFixed(2)}
            </div>
          </div>
        </div>

        {/* Recent Invoices */}
        <div style={{ marginBottom: '30px' }}>
          <h2>📋 Recent Invoices</h2>
          <div style={{
            background: 'white',
            borderRadius: '8px',
            border: '1px solid #dee2e6',
            overflow: 'hidden'
          }}>
            <table style={{ width: '100%', borderCollapse: 'collapse' }}>
              <thead style={{ background: '#f8f9fa' }}>
                <tr>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>Invoice #</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>Customer/Vendor</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>Type</th>
                  <th style={{ padding: '12px', textAlign: 'left', borderBottom: '1px solid #dee2e6' }}>Date</th>
                  <th style={{ padding: '12px', textAlign: 'right', borderBottom: '1px solid #dee2e6' }}>Amount</th>
                  <th style={{ padding: '12px', textAlign: 'center', borderBottom: '1px solid #dee2e6' }}>Status</th>
                </tr>
              </thead>
              <tbody>
                {invoices.map((invoice) => (
                  <tr key={invoice.id} style={{ borderBottom: '1px solid #f8f9fa' }}>
                    <td style={{ padding: '12px' }}>{invoice.ref || invoice.name || `#${invoice.id}`}</td>
                    <td style={{ padding: '12px' }}>{invoice.partner_name}</td>
                    <td style={{ padding: '12px' }}>{getInvoiceTypeLabel(invoice.move_type)}</td>
                    <td style={{ padding: '12px' }}>{new Date(invoice.invoice_date).toLocaleDateString()}</td>
                    <td style={{ padding: '12px', textAlign: 'right', fontWeight: 'bold' }}>
                      ${parseFloat(invoice.amount_total).toFixed(2)}
                    </td>
                    <td style={{ padding: '12px', textAlign: 'center' }}>
                      <span style={{
                        padding: '4px 8px',
                        borderRadius: '4px',
                        color: 'white',
                        fontSize: '0.8em',
                        textTransform: 'uppercase',
                        backgroundColor: getStateColor(invoice.state)
                      }}>
                        {invoice.state}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Chart of Accounts */}
        <div style={{ marginBottom: '30px' }}>
          <h2>📊 Chart of Accounts</h2>
          <div style={{
            display: 'grid',
            gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
            gap: '15px'
          }}>
            {accounts.map((account) => (
              <div key={account.id} style={{
                background: 'white',
                padding: '15px',
                borderRadius: '8px',
                border: '1px solid #dee2e6',
                boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
              }}>
                <div style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  alignItems: 'center',
                  marginBottom: '8px'
                }}>
                  <strong style={{ color: '#007bff' }}>{account.code}</strong>
                  <span style={{
                    padding: '2px 6px',
                    borderRadius: '3px',
                    fontSize: '0.7em',
                    backgroundColor: '#e9ecef',
                    color: '#495057'
                  }}>
                    {account.account_type.replace('_', ' ').toUpperCase()}
                  </span>
                </div>
                <div style={{ color: '#495057' }}>{account.name}</div>
              </div>
            ))}
          </div>
        </div>

        {/* System Status */}
        <div style={{
          background: '#d4edda',
          border: '1px solid #c3e6cb',
          borderRadius: '8px',
          padding: '15px',
          marginBottom: '20px'
        }}>
          <h3 style={{ color: '#155724', margin: '0 0 10px 0' }}>✅ System Status</h3>
          <div style={{ color: '#155724' }}>
            <div>✅ React Frontend: Connected and displaying data</div>
            <div>✅ Django API: Running at http://localhost:8000</div>
            <div>✅ PostgreSQL Database: Connected with {accounts.length + invoices.length} records</div>
            <div>✅ Hybrid Architecture: Fully operational</div>
          </div>
        </div>
      </main>

      <footer style={{
        textAlign: 'center',
        padding: '20px',
        color: '#6c757d',
        borderTop: '1px solid #dee2e6',
        marginTop: '40px'
      }}>
        <p>🚀 Hybrid ERP System - React + Django + PostgreSQL</p>
        <p>Ready for customization and business-specific features</p>
      </footer>
    </div>
  );
}

export default App;
