"""
Serializers for Accounting app
"""
from rest_framework import serializers
from .models import Account, Journal, Invoice, InvoiceLine

class AccountSerializer(serializers.ModelSerializer):
    """
    Serializer for Account model
    """
    class Meta:
        model = Account
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class JournalSerializer(serializers.ModelSerializer):
    """
    Serializer for Journal model
    """
    class Meta:
        model = Journal
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class InvoiceLineSerializer(serializers.ModelSerializer):
    """
    Serializer for Invoice Line model
    """
    class Meta:
        model = InvoiceLine
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at')

class InvoiceSerializer(serializers.ModelSerializer):
    """
    Serializer for Invoice model
    """
    lines = InvoiceLineSerializer(many=True, read_only=True)
    
    class Meta:
        model = Invoice
        fields = '__all__'
        read_only_fields = ('created_at', 'updated_at', 'created_by')
    
    def create(self, validated_data):
        """
        Create invoice and sync with Odoo
        """
        # Set the user who created the invoice
        validated_data['created_by'] = self.context['request'].user
        
        # Create the invoice
        invoice = Invoice.objects.create(**validated_data)
        
        # TODO: Sync with Odoo
        # This will be implemented when we integrate with Odoo
        
        return invoice

class InvoiceCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating invoices with lines
    """
    lines = InvoiceLineSerializer(many=True)
    
    class Meta:
        model = Invoice
        fields = [
            'move_type', 'partner_id', 'partner_name', 'invoice_date',
            'invoice_date_due', 'journal_id', 'ref', 'narration', 'lines'
        ]
    
    def create(self, validated_data):
        """
        Create invoice with lines
        """
        lines_data = validated_data.pop('lines')
        validated_data['created_by'] = self.context['request'].user
        
        # Create invoice
        invoice = Invoice.objects.create(**validated_data)
        
        # Create invoice lines
        total_untaxed = 0
        for line_data in lines_data:
            line_data['invoice'] = invoice
            line = InvoiceLine.objects.create(**line_data)
            total_untaxed += line.price_subtotal
        
        # Update invoice totals
        invoice.amount_untaxed = total_untaxed
        invoice.amount_total = total_untaxed  # TODO: Add tax calculation
        invoice.amount_residual = invoice.amount_total
        invoice.save()
        
        return invoice

class AccountSummarySerializer(serializers.Serializer):
    """
    Serializer for account summary data
    """
    total_receivables = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_payables = serializers.DecimalField(max_digits=16, decimal_places=2)
    total_cash = serializers.DecimalField(max_digits=16, decimal_places=2)
    monthly_revenue = serializers.DecimalField(max_digits=16, decimal_places=2)
    monthly_expenses = serializers.DecimalField(max_digits=16, decimal_places=2)

class DashboardSerializer(serializers.Serializer):
    """
    Serializer for dashboard data
    """
    recent_invoices = InvoiceSerializer(many=True)
    account_summary = AccountSummarySerializer()
    pending_invoices_count = serializers.IntegerField()
    overdue_invoices_count = serializers.IntegerField()
