"""
Views for Accounting app
"""
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum, Q
from datetime import datetime, timedelta

from .models import Account, Journal, Invoice, InvoiceLine
from .serializers import (
    AccountSerializer, JournalSerializer, InvoiceSerializer,
    InvoiceLineSerializer, InvoiceCreateSerializer,
    AccountSummarySerializer, DashboardSerializer
)
from utils.odoo_connector import get_odoo_connector

class AccountViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Chart of Accounts
    """
    queryset = Account.objects.all()
    serializer_class = AccountSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['account_type', 'reconcile', 'deprecated']
    search_fields = ['code', 'name']
    ordering_fields = ['code', 'name']

    @action(detail=False, methods=['post'])
    def sync_from_odoo(self, request):
        """
        Sync accounts from Odoo
        """
        try:
            odoo = get_odoo_connector()
            accounts = odoo.search_read('account.account', fields=[
                'code', 'name', 'account_type', 'internal_type',
                'reconcile', 'deprecated', 'currency_id', 'company_id'
            ])

            synced_count = 0
            for acc_data in accounts:
                account, created = Account.objects.update_or_create(
                    odoo_id=acc_data['id'],
                    defaults={
                        'code': acc_data['code'],
                        'name': acc_data['name'],
                        'account_type': acc_data.get('account_type', ''),
                        'internal_type': acc_data.get('internal_type', ''),
                        'reconcile': acc_data.get('reconcile', False),
                        'deprecated': acc_data.get('deprecated', False),
                        'currency_id': acc_data.get('currency_id', [None])[0] if acc_data.get('currency_id') else None,
                        'company_id': acc_data.get('company_id', [None])[0] if acc_data.get('company_id') else None,
                    }
                )
                if created:
                    synced_count += 1

            return Response({
                'message': f'Successfully synced {synced_count} accounts from Odoo',
                'total_accounts': Account.objects.count()
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to sync accounts: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class JournalViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Journals
    """
    queryset = Journal.objects.all()
    serializer_class = JournalSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['type', 'active']
    search_fields = ['name', 'code']
    ordering_fields = ['sequence', 'name']

    @action(detail=False, methods=['post'])
    def sync_from_odoo(self, request):
        """
        Sync journals from Odoo
        """
        try:
            odoo = get_odoo_connector()
            journals = odoo.search_read('account.journal', fields=[
                'name', 'code', 'type', 'sequence', 'active', 'company_id'
            ])

            synced_count = 0
            for journal_data in journals:
                journal, created = Journal.objects.update_or_create(
                    odoo_id=journal_data['id'],
                    defaults={
                        'name': journal_data['name'],
                        'code': journal_data['code'],
                        'type': journal_data.get('type', ''),
                        'sequence': journal_data.get('sequence', 10),
                        'active': journal_data.get('active', True),
                        'company_id': journal_data.get('company_id', [None])[0] if journal_data.get('company_id') else None,
                    }
                )
                if created:
                    synced_count += 1

            return Response({
                'message': f'Successfully synced {synced_count} journals from Odoo',
                'total_journals': Journal.objects.count()
            })
        except Exception as e:
            return Response(
                {'error': f'Failed to sync journals: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class InvoiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing Invoices
    """
    queryset = Invoice.objects.all()
    serializer_class = InvoiceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['move_type', 'state', 'partner_id']
    search_fields = ['name', 'partner_name', 'ref']
    ordering_fields = ['invoice_date', 'amount_total']

    def get_serializer_class(self):
        """
        Return appropriate serializer based on action
        """
        if self.action == 'create':
            return InvoiceCreateSerializer
        return InvoiceSerializer

    @action(detail=False, methods=['get'])
    def dashboard(self, request):
        """
        Get dashboard data for accounting
        """
        try:
            # Recent invoices
            recent_invoices = Invoice.objects.filter(
                state__in=['draft', 'posted']
            ).order_by('-created_at')[:5]

            # Account summary
            current_month = datetime.now().replace(day=1)

            total_receivables = Invoice.objects.filter(
                move_type='out_invoice',
                state='posted'
            ).aggregate(total=Sum('amount_residual'))['total'] or 0

            total_payables = Invoice.objects.filter(
                move_type='in_invoice',
                state='posted'
            ).aggregate(total=Sum('amount_residual'))['total'] or 0

            monthly_revenue = Invoice.objects.filter(
                move_type='out_invoice',
                state='posted',
                invoice_date__gte=current_month
            ).aggregate(total=Sum('amount_total'))['total'] or 0

            monthly_expenses = Invoice.objects.filter(
                move_type='in_invoice',
                state='posted',
                invoice_date__gte=current_month
            ).aggregate(total=Sum('amount_total'))['total'] or 0

            # Counts
            pending_invoices_count = Invoice.objects.filter(state='draft').count()
            overdue_invoices_count = Invoice.objects.filter(
                state='posted',
                invoice_date_due__lt=datetime.now().date(),
                amount_residual__gt=0
            ).count()

            dashboard_data = {
                'recent_invoices': recent_invoices,
                'account_summary': {
                    'total_receivables': total_receivables,
                    'total_payables': total_payables,
                    'total_cash': 0,  # TODO: Calculate from bank accounts
                    'monthly_revenue': monthly_revenue,
                    'monthly_expenses': monthly_expenses,
                },
                'pending_invoices_count': pending_invoices_count,
                'overdue_invoices_count': overdue_invoices_count,
            }

            serializer = DashboardSerializer(dashboard_data)
            return Response(serializer.data)

        except Exception as e:
            return Response(
                {'error': f'Failed to get dashboard data: {str(e)}'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
