from django.db import models
from django.contrib.auth.models import User
from utils.odoo_connector import get_odoo_connector

class AccountingMixin:
    """
    Mixin to provide Odoo integration methods for accounting models
    """

    @classmethod
    def sync_from_odoo(cls, odoo_model, domain=None):
        """
        Sync records from Odoo to Django
        """
        odoo = get_odoo_connector()
        records = odoo.search_read(odoo_model, domain=domain)
        return records

    def push_to_odoo(self, odoo_model, values):
        """
        Push record to Odoo
        """
        odoo = get_odoo_connector()
        if hasattr(self, 'odoo_id') and self.odoo_id:
            # Update existing record
            return odoo.write(odoo_model, [self.odoo_id], values)
        else:
            # Create new record
            odoo_id = odoo.create(odoo_model, values)
            self.odoo_id = odoo_id
            self.save()
            return odoo_id

class Account(models.Model):
    """
    Chart of Accounts - mirrors Odoo account.account
    """
    odoo_id = models.IntegerField(unique=True, null=True, blank=True)
    code = models.CharField(max_length=64)
    name = models.CharField(max_length=256)
    account_type = models.CharField(max_length=64)
    internal_type = models.CharField(max_length=64, null=True, blank=True)
    reconcile = models.BooleanField(default=False)
    deprecated = models.BooleanField(default=False)
    currency_id = models.IntegerField(null=True, blank=True)
    company_id = models.IntegerField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounting_account'
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

class Journal(models.Model):
    """
    Accounting Journals - mirrors Odoo account.journal
    """
    odoo_id = models.IntegerField(unique=True, null=True, blank=True)
    name = models.CharField(max_length=64)
    code = models.CharField(max_length=5)
    type = models.CharField(max_length=32)
    sequence = models.IntegerField(default=10)
    active = models.BooleanField(default=True)
    company_id = models.IntegerField(null=True, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounting_journal'
        ordering = ['sequence', 'name']

    def __str__(self):
        return f"{self.code} - {self.name}"

class Invoice(models.Model):
    """
    Customer/Vendor Invoices - mirrors Odoo account.move
    """
    MOVE_TYPE_CHOICES = [
        ('out_invoice', 'Customer Invoice'),
        ('in_invoice', 'Vendor Bill'),
        ('out_refund', 'Customer Credit Note'),
        ('in_refund', 'Vendor Credit Note'),
    ]

    STATE_CHOICES = [
        ('draft', 'Draft'),
        ('posted', 'Posted'),
        ('cancel', 'Cancelled'),
    ]

    odoo_id = models.IntegerField(unique=True, null=True, blank=True)
    name = models.CharField(max_length=64, null=True, blank=True)
    move_type = models.CharField(max_length=32, choices=MOVE_TYPE_CHOICES)
    state = models.CharField(max_length=32, choices=STATE_CHOICES, default='draft')

    partner_id = models.IntegerField()  # Customer/Vendor ID from Odoo
    partner_name = models.CharField(max_length=256)

    invoice_date = models.DateField()
    invoice_date_due = models.DateField(null=True, blank=True)

    journal_id = models.IntegerField()
    currency_id = models.IntegerField(null=True, blank=True)
    company_id = models.IntegerField(null=True, blank=True)

    amount_untaxed = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_tax = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    amount_residual = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    ref = models.CharField(max_length=256, null=True, blank=True)
    narration = models.TextField(null=True, blank=True)

    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounting_invoice'
        ordering = ['-invoice_date', '-id']

    def __str__(self):
        return f"{self.name or 'Draft'} - {self.partner_name}"

class InvoiceLine(models.Model):
    """
    Invoice Lines - mirrors Odoo account.move.line
    """
    odoo_id = models.IntegerField(unique=True, null=True, blank=True)
    invoice = models.ForeignKey(Invoice, on_delete=models.CASCADE, related_name='lines')

    product_id = models.IntegerField(null=True, blank=True)
    product_name = models.CharField(max_length=256)

    account_id = models.IntegerField()

    quantity = models.DecimalField(max_digits=16, decimal_places=2, default=1)
    price_unit = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_subtotal = models.DecimalField(max_digits=16, decimal_places=2, default=0)
    price_total = models.DecimalField(max_digits=16, decimal_places=2, default=0)

    tax_ids = models.JSONField(default=list, blank=True)  # List of tax IDs

    name = models.TextField()  # Description

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'accounting_invoice_line'

    def __str__(self):
        return f"{self.product_name} - {self.quantity} x {self.price_unit}"
