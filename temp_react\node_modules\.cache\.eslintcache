[{"D:\\odoo_erp\\temp_react\\src\\index.tsx": "1", "D:\\odoo_erp\\temp_react\\src\\reportWebVitals.ts": "2", "D:\\odoo_erp\\temp_react\\src\\App.tsx": "3"}, {"size": 554, "mtime": 1752400414858, "results": "4", "hashOfConfig": "5"}, {"size": 425, "mtime": 1752400411765, "results": "6", "hashOfConfig": "5"}, {"size": 4292, "mtime": 1752412077198, "results": "7", "hashOfConfig": "5"}, {"filePath": "8", "messages": "9", "suppressedMessages": "10", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "68sips", {"filePath": "11", "messages": "12", "suppressedMessages": "13", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "14", "messages": "15", "suppressedMessages": "16", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\odoo_erp\\temp_react\\src\\index.tsx", [], [], "D:\\odoo_erp\\temp_react\\src\\reportWebVitals.ts", [], [], "D:\\odoo_erp\\temp_react\\src\\App.tsx", [], []]