# Odoo Accounting Module Setup Guide

This guide walks you through setting up the core accounting module in Odoo Community Edition.

## Prerequisites
- Odoo Community Edition installed and running
- Database created and accessible
- Admin access to Odoo instance

## Step 1: Initial Database Setup

1. Open browser and navigate to `http://localhost:8069`
2. Create a new database:
   - Database Name: `odoo_erp`
   - Email: Your admin email
   - Password: Strong admin password
   - Phone: Your phone number
   - Language: English (or your preferred language)
   - Country: Your country (important for accounting localization)

## Step 2: Install Accounting Module

1. Go to **Apps** menu
2. Search for "Accounting"
3. Click **Install** on the "Accounting" app
4. Wait for installation to complete

## Step 3: Basic Accounting Configuration

### Company Information
1. Go to **Settings** → **General Settings**
2. Under **Companies**, click **Update Info**
3. Fill in:
   - Company Name
   - Address
   - Tax ID/VAT Number
   - Currency
   - Fiscal Year End

### Chart of Accounts
1. Go to **Accounting** → **Configuration** → **Chart of Accounts**
2. Review the default chart of accounts
3. Customize as needed for your business:
   - Add new accounts
   - Modify existing accounts
   - Set account codes according to your standards

### Journals Configuration
1. Go to **Accounting** → **Configuration** → **Journals**
2. Configure default journals:
   - **Sales Journal** - for customer invoices
   - **Purchase Journal** - for vendor bills
   - **Bank Journal** - for bank transactions
   - **Cash Journal** - for cash transactions
   - **Miscellaneous Journal** - for manual entries

### Tax Configuration
1. Go to **Accounting** → **Configuration** → **Taxes**
2. Set up tax rates according to your country:
   - Sales taxes
   - Purchase taxes
   - Tax groups
   - Tax positions for different customer types

### Fiscal Positions
1. Go to **Accounting** → **Configuration** → **Fiscal Positions**
2. Set up fiscal positions for:
   - Domestic customers
   - International customers
   - Different tax scenarios

## Step 4: Bank Account Setup

1. Go to **Accounting** → **Configuration** → **Banks**
2. Add your bank accounts:
   - Bank name
   - Account number
   - IBAN/SWIFT codes
   - Currency

## Step 5: Payment Terms

1. Go to **Accounting** → **Configuration** → **Payment Terms**
2. Set up common payment terms:
   - Immediate payment
   - 30 days
   - 60 days
   - Custom terms

## Step 6: Customer and Vendor Setup

### Customers
1. Go to **Accounting** → **Customers** → **Customers**
2. Create customer records with:
   - Contact information
   - Payment terms
   - Fiscal position
   - Credit limit

### Vendors
1. Go to **Accounting** → **Vendors** → **Vendors**
2. Create vendor records with:
   - Contact information
   - Payment terms
   - Fiscal position

## Step 7: Initial Balances

1. Go to **Accounting** → **Accounting** → **Journal Entries**
2. Create opening balance entries:
   - Assets
   - Liabilities
   - Equity
   - Outstanding customer invoices
   - Outstanding vendor bills

## Step 8: Basic Workflows

### Creating Customer Invoices
1. Go to **Accounting** → **Customers** → **Invoices**
2. Click **Create**
3. Fill in customer, products, and amounts
4. Post the invoice

### Recording Vendor Bills
1. Go to **Accounting** → **Vendors** → **Bills**
2. Click **Create**
3. Fill in vendor, products, and amounts
4. Post the bill

### Bank Reconciliation
1. Go to **Accounting** → **Accounting** → **Bank**
2. Import bank statements or enter manually
3. Reconcile transactions with journal entries

## Step 9: Reports Setup

Configure key accounting reports:
1. **Profit & Loss** - Income statement
2. **Balance Sheet** - Financial position
3. **General Ledger** - Detailed account transactions
4. **Trial Balance** - Account balances verification
5. **Aged Receivables** - Customer payment tracking
6. **Aged Payables** - Vendor payment tracking

## Step 10: User Access Rights

1. Go to **Settings** → **Users & Companies** → **Users**
2. Set up user roles:
   - **Accounting Manager** - Full access
   - **Billing** - Invoice creation only
   - **Show Accounting Features** - Read-only access

## Next Steps

After completing the accounting setup:
1. Test the complete invoice-to-payment cycle
2. Verify all reports are working correctly
3. Train users on basic operations
4. Prepare for integration with Purchasing module

## Common Issues and Solutions

### Issue: Wrong Chart of Accounts
**Solution**: Go to Apps → Search "Localization" → Install your country's localization package

### Issue: Tax Calculation Errors
**Solution**: Check tax configuration and fiscal positions

### Issue: Bank Reconciliation Problems
**Solution**: Verify bank journal configuration and account mapping

## Best Practices

1. **Regular Backups**: Set up automated database backups
2. **User Training**: Ensure all users understand their roles
3. **Monthly Closing**: Establish month-end procedures
4. **Documentation**: Keep records of all configuration changes
5. **Testing**: Test all workflows before going live
