{"ast": null, "code": "'use strict';\n\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\nvar $Object = Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !$Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) === 'String' ? split(it, '') : $Object(it);\n} : $Object;", "map": {"version": 3, "names": ["uncurryThis", "require", "fails", "classof", "$Object", "Object", "split", "module", "exports", "propertyIsEnumerable", "it"], "sources": ["D:/odoo_erp/temp_react/node_modules/core-js-pure/internals/indexed-object.js"], "sourcesContent": ["'use strict';\nvar uncurryThis = require('../internals/function-uncurry-this');\nvar fails = require('../internals/fails');\nvar classof = require('../internals/classof-raw');\n\nvar $Object = Object;\nvar split = uncurryThis(''.split);\n\n// fallback for non-array-like ES3 and non-enumerable old V8 strings\nmodule.exports = fails(function () {\n  // throws an error in rhino, see https://github.com/mozilla/rhino/issues/346\n  // eslint-disable-next-line no-prototype-builtins -- safe\n  return !$Object('z').propertyIsEnumerable(0);\n}) ? function (it) {\n  return classof(it) === 'String' ? split(it, '') : $Object(it);\n} : $Object;\n"], "mappings": "AAAA,YAAY;;AACZ,IAAIA,WAAW,GAAGC,OAAO,CAAC,oCAAoC,CAAC;AAC/D,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAoB,CAAC;AACzC,IAAIE,OAAO,GAAGF,OAAO,CAAC,0BAA0B,CAAC;AAEjD,IAAIG,OAAO,GAAGC,MAAM;AACpB,IAAIC,KAAK,GAAGN,WAAW,CAAC,EAAE,CAACM,KAAK,CAAC;;AAEjC;AACAC,MAAM,CAACC,OAAO,GAAGN,KAAK,CAAC,YAAY;EACjC;EACA;EACA,OAAO,CAACE,OAAO,CAAC,GAAG,CAAC,CAACK,oBAAoB,CAAC,CAAC,CAAC;AAC9C,CAAC,CAAC,GAAG,UAAUC,EAAE,EAAE;EACjB,OAAOP,OAAO,CAACO,EAAE,CAAC,KAAK,QAAQ,GAAGJ,KAAK,CAACI,EAAE,EAAE,EAAE,CAAC,GAAGN,OAAO,CAACM,EAAE,CAAC;AAC/D,CAAC,GAAGN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}