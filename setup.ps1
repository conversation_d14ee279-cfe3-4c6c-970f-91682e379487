# Odoo ERP Setup Script for Windows
# Run this script as Administrator

Write-Host "=== Odoo Community Edition Setup ===" -ForegroundColor Green

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "This script requires Administrator privileges. Please run as Administrator." -ForegroundColor Red
    exit 1
}

# Function to check if a command exists
function Test-Command($cmdname) {
    return [bool](Get-Command -Name $cmdname -ErrorAction SilentlyContinue)
}

Write-Host "Checking prerequisites..." -ForegroundColor Yellow

# Check Python
if (Test-Command python) {
    $pythonVersion = python --version
    Write-Host "✅ Python found: $pythonVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Python not found. Please install Python 3.8+" -ForegroundColor Red
    exit 1
}

# Check Git
if (Test-Command git) {
    $gitVersion = git --version
    Write-Host "✅ Git found: $gitVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Git not found. Installing Git..." -ForegroundColor Yellow
    # Install Git using winget if available
    if (Test-Command winget) {
        winget install --id Git.Git -e --source winget
    } else {
        Write-Host "Please install Git manually from https://git-scm.com/download/win" -ForegroundColor Red
        exit 1
    }
}

# Check PostgreSQL
if (Test-Command psql) {
    $pgVersion = psql --version
    Write-Host "✅ PostgreSQL found: $pgVersion" -ForegroundColor Green
} else {
    Write-Host "❌ PostgreSQL not found." -ForegroundColor Red
    Write-Host "Please install PostgreSQL from https://www.postgresql.org/download/windows/" -ForegroundColor Yellow
    Write-Host "Remember the password for the 'postgres' user during installation." -ForegroundColor Yellow
    exit 1
}

# Check Node.js
if (Test-Command node) {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} else {
    Write-Host "❌ Node.js not found. Installing Node.js..." -ForegroundColor Yellow
    if (Test-Command winget) {
        winget install --id OpenJS.NodeJS -e --source winget
    } else {
        Write-Host "Please install Node.js manually from https://nodejs.org/" -ForegroundColor Red
        exit 1
    }
}

Write-Host "Prerequisites check completed!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. Ensure PostgreSQL service is running" -ForegroundColor White
Write-Host "2. Run: python setup_odoo.py" -ForegroundColor White
