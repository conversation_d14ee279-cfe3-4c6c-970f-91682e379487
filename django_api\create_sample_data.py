#!/usr/bin/env python
"""
Create sample accounting data for testing
"""
import os
import sys
import django
from datetime import date, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from apps.accounting.models import Account, Journal, Invoice, InvoiceLine
from django.contrib.auth.models import User

def create_sample_data():
    print("🔧 Creating sample accounting data...")
    
    # Create sample accounts
    accounts_data = [
        {'code': '1000', 'name': 'Cash', 'account_type': 'asset_current'},
        {'code': '1200', 'name': 'Accounts Receivable', 'account_type': 'asset_receivable'},
        {'code': '2000', 'name': 'Accounts Payable', 'account_type': 'liability_payable'},
        {'code': '3000', 'name': 'Capital', 'account_type': 'equity'},
        {'code': '4000', 'name': 'Sales Revenue', 'account_type': 'income'},
        {'code': '5000', 'name': 'Office Expenses', 'account_type': 'expense'},
        {'code': '5100', 'name': 'Marketing Expenses', 'account_type': 'expense'},
    ]
    
    print("📊 Creating accounts...")
    for acc_data in accounts_data:
        account, created = Account.objects.get_or_create(
            code=acc_data['code'],
            defaults=acc_data
        )
        if created:
            print(f"  ✅ Created account: {account.code} - {account.name}")
    
    # Create sample journals
    journals_data = [
        {'name': 'Sales Journal', 'code': 'SAL', 'type': 'sale'},
        {'name': 'Purchase Journal', 'code': 'PUR', 'type': 'purchase'},
        {'name': 'Cash Journal', 'code': 'CSH', 'type': 'cash'},
        {'name': 'Bank Journal', 'code': 'BNK', 'type': 'bank'},
    ]
    
    print("📝 Creating journals...")
    for journal_data in journals_data:
        journal, created = Journal.objects.get_or_create(
            code=journal_data['code'],
            defaults=journal_data
        )
        if created:
            print(f"  ✅ Created journal: {journal.code} - {journal.name}")
    
    # Get admin user
    admin_user = User.objects.get(username='admin')
    
    # Create sample invoices
    print("🧾 Creating sample invoices...")
    
    # Customer Invoice 1
    invoice1 = Invoice.objects.create(
        move_type='out_invoice',
        partner_id=1,
        partner_name='ABC Company Ltd.',
        invoice_date=date.today() - timedelta(days=5),
        invoice_date_due=date.today() + timedelta(days=25),
        journal_id=1,
        amount_untaxed=1000.00,
        amount_tax=100.00,
        amount_total=1100.00,
        amount_residual=1100.00,
        ref='INV-001',
        created_by=admin_user,
        state='posted'
    )
    
    # Invoice lines for invoice 1
    InvoiceLine.objects.create(
        invoice=invoice1,
        product_name='Web Development Services',
        account_id=4000,
        quantity=1,
        price_unit=1000.00,
        price_subtotal=1000.00,
        price_total=1100.00,
        name='Custom website development and design'
    )
    
    # Customer Invoice 2
    invoice2 = Invoice.objects.create(
        move_type='out_invoice',
        partner_id=2,
        partner_name='XYZ Corporation',
        invoice_date=date.today() - timedelta(days=2),
        invoice_date_due=date.today() + timedelta(days=28),
        journal_id=1,
        amount_untaxed=750.00,
        amount_tax=75.00,
        amount_total=825.00,
        amount_residual=825.00,
        ref='INV-002',
        created_by=admin_user,
        state='posted'
    )
    
    InvoiceLine.objects.create(
        invoice=invoice2,
        product_name='Consulting Services',
        account_id=4000,
        quantity=5,
        price_unit=150.00,
        price_subtotal=750.00,
        price_total=825.00,
        name='Business process consulting - 5 hours'
    )
    
    # Vendor Bill 1
    bill1 = Invoice.objects.create(
        move_type='in_invoice',
        partner_id=3,
        partner_name='Office Supplies Co.',
        invoice_date=date.today() - timedelta(days=3),
        invoice_date_due=date.today() + timedelta(days=27),
        journal_id=2,
        amount_untaxed=250.00,
        amount_tax=25.00,
        amount_total=275.00,
        amount_residual=275.00,
        ref='BILL-001',
        created_by=admin_user,
        state='posted'
    )
    
    InvoiceLine.objects.create(
        invoice=bill1,
        product_name='Office Supplies',
        account_id=5000,
        quantity=1,
        price_unit=250.00,
        price_subtotal=250.00,
        price_total=275.00,
        name='Stationery, printer paper, and office materials'
    )
    
    # Draft Invoice
    draft_invoice = Invoice.objects.create(
        move_type='out_invoice',
        partner_id=4,
        partner_name='New Client Ltd.',
        invoice_date=date.today(),
        invoice_date_due=date.today() + timedelta(days=30),
        journal_id=1,
        amount_untaxed=500.00,
        amount_tax=50.00,
        amount_total=550.00,
        amount_residual=550.00,
        ref='DRAFT-001',
        created_by=admin_user,
        state='draft'
    )
    
    InvoiceLine.objects.create(
        invoice=draft_invoice,
        product_name='Software License',
        account_id=4000,
        quantity=1,
        price_unit=500.00,
        price_subtotal=500.00,
        price_total=550.00,
        name='Annual software license renewal'
    )
    
    print("✅ Sample data created successfully!")
    print(f"📊 Created {Account.objects.count()} accounts")
    print(f"📝 Created {Journal.objects.count()} journals") 
    print(f"🧾 Created {Invoice.objects.count()} invoices")
    print(f"📋 Created {InvoiceLine.objects.count()} invoice lines")
    
    print("\n🌐 You can now view the data at:")
    print("  - Django Admin: http://localhost:8000/admin")
    print("  - API Endpoints:")
    print("    - Accounts: http://localhost:8000/api/accounting/accounts/")
    print("    - Journals: http://localhost:8000/api/accounting/journals/")
    print("    - Invoices: http://localhost:8000/api/accounting/invoices/")

if __name__ == '__main__':
    create_sample_data()
