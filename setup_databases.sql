-- Database setup script for Hybrid ERP System
-- Run this script as PostgreSQL superuser (postgres)

-- Create user for Odoo and Django
CREATE USER odoo WITH CREATEDB PASSWORD 'odoo';

-- Create databases
CREATE DATABASE odoo_erp OWNER odoo;
CREATE DATABASE django_erp OWNER odoo;

-- Grant all privileges
GRANT ALL PRIVILEGES ON DATABASE odoo_erp TO odoo;
GRANT ALL PRIVILEGES ON DATABASE django_erp TO odoo;

-- Display created databases
\l

-- Display users
\du
