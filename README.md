# Odoo Community Edition ERP Setup

This project sets up Odoo Community Edition with a focus on accounting as the core module, then progressively integrating purchasing, inventory, sales, HR, and other modules.

## Prerequisites

### Required Software
1. **Python 3.8+** ✅ (Already installed)
2. **PostgreSQL 12+** ❌ (Needs installation)
3. **Git** ❌ (Needs installation)
4. **Node.js** (for web assets compilation)
5. **wkhtmltopdf** (for PDF reports)

### Installation Steps

#### 1. Install PostgreSQL
- Download from: https://www.postgresql.org/download/windows/
- During installation, remember the password for the `postgres` user
- Default port: 5432

#### 2. Install Git
- Download from: https://git-scm.com/download/win
- Use default settings during installation

#### 3. Install Node.js
- Download from: https://nodejs.org/
- Choose LTS version

#### 4. Install wkhtmltopdf
- Download from: https://wkhtmltopdf.org/downloads.html
- Choose Windows version

## Odoo Installation Options

### Option 1: Install from Source (Recommended for Development)
```bash
# Clone Odoo Community Edition
git clone https://github.com/odoo/odoo.git --depth 1 --branch 17.0

# Install Python dependencies
pip install -r odoo/requirements.txt

# Additional Windows-specific dependencies
pip install psycopg2-binary
```

### Option 2: Install via pip
```bash
pip install odoo
```

## Database Setup

1. Create PostgreSQL user for Odoo:
```sql
CREATE USER odoo WITH CREATEDB PASSWORD 'odoo';
```

2. Create database:
```sql
CREATE DATABASE odoo_erp OWNER odoo;
```

## Configuration

Create `odoo.conf` file with basic configuration:
```ini
[options]
admin_passwd = admin
db_host = localhost
db_port = 5432
db_user = odoo
db_password = odoo
addons_path = addons
```

## Module Integration Plan

1. **Core Accounting** - Foundation module
2. **Purchasing** - Vendor management and purchase orders
3. **Inventory** - Stock management and warehouse operations
4. **Sales** - Customer management and sales orders
5. **HR** - Human resources and payroll
6. **Additional Modules** - CRM, Project, Manufacturing, etc.

## Next Steps

After installing prerequisites, we'll:
1. Set up the Odoo environment
2. Configure the accounting module
3. Progressively integrate other modules
